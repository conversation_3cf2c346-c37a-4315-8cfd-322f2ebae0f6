import React, { useState, useRef, useEffect } from 'react';

interface MultiSelectDropdownProps {
  label: string;
  options: string[];
  selectedValues: string[];
  onChange: (values: string[]) => void;
  placeholder?: string;
}

const MultiSelectDropdown: React.FC<MultiSelectDropdownProps> = ({
  label,
  options,
  selectedValues,
  onChange,
  placeholder = "Select options..."
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleToggleOption = (option: string) => {
    if (selectedValues.includes(option)) {
      onChange(selectedValues.filter(value => value !== option));
    } else {
      onChange([...selectedValues, option]);
    }
  };

  const handleSelectAll = () => {
    if (selectedValues.length === options.length) {
      onChange([]);
    } else {
      onChange([...options]);
    }
  };

  const getDisplayText = () => {
    if (selectedValues.length === 0) {
      return placeholder;
    } else if (selectedValues.length === 1) {
      return selectedValues[0];
    } else if (selectedValues.length === options.length) {
      return `All ${label.toLowerCase()}`;
    } else {
      return `${selectedValues.length} selected`;
    }
  };

  return (
    <div className="flex flex-col" ref={dropdownRef}>
      <label className="text-white/80 text-sm font-medium mb-2">
        {label} ({options.length} available)
      </label>
      
      <div className="relative">
        <button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          className="w-full px-4 py-2 rounded-lg bg-white/20 text-white border border-white/30 focus:border-white/60 focus:outline-none transition-all duration-300 min-w-[200px] text-left flex items-center justify-between"
        >
          <span className={selectedValues.length === 0 ? 'text-white/70' : 'text-white'}>
            {getDisplayText()}
          </span>
          <svg
            className={`w-4 h-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>

        {isOpen && (
          <div className="absolute z-50 w-full mt-1 bg-gray-800 border border-white/30 rounded-lg shadow-xl max-h-60 overflow-y-auto">
            {/* Select All Option */}
            <div className="px-4 py-2 border-b border-white/20">
              <label className="flex items-center cursor-pointer hover:bg-white/10 transition-colors duration-200 rounded px-2 py-1">
                <input
                  type="checkbox"
                  checked={selectedValues.length === options.length}
                  onChange={handleSelectAll}
                  className="mr-3 w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2"
                />
                <span className="text-white font-medium">
                  {selectedValues.length === options.length ? 'Deselect All' : 'Select All'}
                </span>
              </label>
            </div>

            {/* Individual Options */}
            {options.map((option) => (
              <div key={option} className="px-4 py-1">
                <label className="flex items-center cursor-pointer hover:bg-white/10 transition-colors duration-200 rounded px-2 py-2">
                  <input
                    type="checkbox"
                    checked={selectedValues.includes(option)}
                    onChange={() => handleToggleOption(option)}
                    className="mr-3 w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2"
                  />
                  <span className="text-white">{option}</span>
                </label>
              </div>
            ))}

            {options.length === 0 && (
              <div className="px-4 py-3 text-white/70 text-center">
                No options available
              </div>
            )}
          </div>
        )}
      </div>

      {/* Selected items display */}
      {selectedValues.length > 0 && selectedValues.length < options.length && (
        <div className="mt-2 flex flex-wrap gap-1">
          {selectedValues.map((value) => (
            <span
              key={value}
              className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gradient-to-r from-blue-500 to-purple-500 text-white"
            >
              {value}
              <button
                onClick={() => handleToggleOption(value)}
                className="ml-1 hover:text-red-300 transition-colors"
              >
                ×
              </button>
            </span>
          ))}
        </div>
      )}
    </div>
  );
};

export default MultiSelectDropdown;
