import React, { useState, useEffect } from 'react';
import type { ModelScore, Category } from '../types';
import { getCategories } from '../utils/storage';
import MultiSelectDropdown from './MultiSelectDropdown';

interface LeaderboardProps {
  scores: ModelScore[];
  onProvidersChange: (providers: string[]) => void;
  onCategoriesChange: (categories: string[]) => void;
  availableProviders: string[];
  selectedProviders: string[];
  selectedCategories: string[];
}

const Leaderboard: React.FC<LeaderboardProps> = ({
  scores,
  onProvidersChange,
  onCategoriesChange,
  availableProviders,
  selectedProviders,
  selectedCategories
}) => {
  const [categories, setCategories] = useState<Category[]>([]);

  useEffect(() => {
    setCategories(getCategories());
  }, []);
  const getRankColor = (rank: number): string => {
    switch (rank) {
      case 1: return 'bg-gradient-to-r from-yellow-400 to-yellow-600 text-white shadow-lg scale-110';
      case 2: return 'bg-gradient-to-r from-gray-400 to-gray-600 text-white shadow-lg scale-105';
      case 3: return 'bg-gradient-to-r from-orange-400 to-orange-600 text-white shadow-lg scale-102';
      default: return 'bg-gradient-to-r from-blue-400 to-blue-600 text-white';
    }
  };

  const getRankIcon = (rank: number): string => {
    switch (rank) {
      case 1: return '🥇';
      case 2: return '🥈';
      case 3: return '🥉';
      default: return `#${rank}`;
    }
  };

  const getPassRateColor = (passRate: number): string => {
    if (passRate >= 80) return 'bg-gradient-to-r from-green-500 to-emerald-500 text-white';
    if (passRate >= 60) return 'bg-gradient-to-r from-yellow-500 to-amber-500 text-white';
    if (passRate >= 40) return 'bg-gradient-to-r from-orange-500 to-red-500 text-white';
    return 'bg-gradient-to-r from-red-500 to-pink-500 text-white';
  };

  if (scores.length === 0) {
    const hasFilters = selectedProviders.length > 0 || selectedCategories.length > 0;
    return (
      <div className="space-y-6">
        {/* Show filters even when no results */}
        <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
          <h2 className="text-3xl font-bold gradient-text float-animation">
            🏆 Leaderboard
          </h2>

          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Provider Filter */}
            <MultiSelectDropdown
              label="Filter by Provider"
              options={availableProviders}
              selectedValues={selectedProviders}
              onChange={onProvidersChange}
              placeholder="All Providers"
            />

            {/* Category Filter */}
            <MultiSelectDropdown
              label="Filter by Category"
              options={categories.map(cat => cat.name)}
              selectedValues={selectedCategories}
              onChange={onCategoriesChange}
              placeholder="All Categories"
            />
          </div>
        </div>

        {/* Filter Status Display */}
        {hasFilters && (
          <div className="glass-effect rounded-lg p-4">
            <div className="flex flex-wrap items-center justify-between gap-2">
              <div className="flex flex-wrap items-center gap-2 text-white/80">
                <span className="font-medium">Active filters:</span>
                {selectedProviders.map(provider => (
                  <span key={provider} className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-gradient-to-r from-blue-500 to-purple-500 text-white">
                    Provider: {provider}
                    <button
                      onClick={() => onProvidersChange(selectedProviders.filter(p => p !== provider))}
                      className="ml-2 hover:text-red-300 transition-colors"
                    >
                      ×
                    </button>
                  </span>
                ))}
                {selectedCategories.map(category => (
                  <span key={category} className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-gradient-to-r from-green-500 to-teal-500 text-white">
                    Category: {category}
                    <button
                      onClick={() => onCategoriesChange(selectedCategories.filter(c => c !== category))}
                      className="ml-2 hover:text-red-300 transition-colors"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
              <button
                onClick={() => {
                  onProvidersChange([]);
                  onCategoriesChange([]);
                }}
                className="btn-animated px-4 py-2 rounded-lg text-sm bg-gradient-to-r from-red-500 to-pink-500 text-white hover:from-red-600 hover:to-pink-600 transition-all duration-300"
              >
                🗑️ Clear All Filters
              </button>
            </div>
          </div>
        )}

        <div className="text-center py-12">
          <div className="text-6xl mb-4 bounce-animation">
            {hasFilters ? '🔍' : '🏆'}
          </div>
          <h3 className="text-2xl font-bold text-white mb-2">
            {hasFilters ? 'No Results Found' : 'No Results Yet'}
          </h3>
          <p className="text-white/70 text-lg">
            {hasFilters
              ? 'Try adjusting your filters or clear them to see all results.'
              : 'Start testing models to see the leaderboard!'
            }
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
        <h2 className="text-3xl font-bold gradient-text float-animation">
          🏆 Leaderboard
        </h2>

        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Provider Filter */}
          <MultiSelectDropdown
            label="Filter by Provider"
            options={availableProviders}
            selectedValues={selectedProviders}
            onChange={onProvidersChange}
            placeholder="All Providers"
          />

          {/* Category Filter */}
          <MultiSelectDropdown
            label="Filter by Category"
            options={categories.map(cat => cat.name)}
            selectedValues={selectedCategories}
            onChange={onCategoriesChange}
            placeholder="All Categories"
          />
        </div>
      </div>

      {/* Filter Status Display */}
      {(selectedProviders.length > 0 || selectedCategories.length > 0) && (
        <div className="glass-effect rounded-lg p-4">
          <div className="flex flex-wrap items-center justify-between gap-2">
            <div className="flex flex-wrap items-center gap-2 text-white/80">
              <span className="font-medium">Active filters:</span>
              {selectedProviders.map(provider => (
                <span key={provider} className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-gradient-to-r from-blue-500 to-purple-500 text-white">
                  Provider: {provider}
                  <button
                    onClick={() => onProvidersChange(selectedProviders.filter(p => p !== provider))}
                    className="ml-2 hover:text-red-300 transition-colors"
                  >
                    ×
                  </button>
                </span>
              ))}
              {selectedCategories.map(category => (
                <span key={category} className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-gradient-to-r from-green-500 to-teal-500 text-white">
                  Category: {category}
                  <button
                    onClick={() => onCategoriesChange(selectedCategories.filter(c => c !== category))}
                    className="ml-2 hover:text-red-300 transition-colors"
                  >
                    ×
                  </button>
                </span>
              ))}
            </div>
            <button
              onClick={() => {
                onProvidersChange([]);
                onCategoriesChange([]);
              }}
              className="btn-animated px-4 py-2 rounded-lg text-sm bg-gradient-to-r from-red-500 to-pink-500 text-white hover:from-red-600 hover:to-pink-600 transition-all duration-300"
            >
              🗑️ Clear All Filters
            </button>
          </div>
        </div>
      )}

      <div className="glass-effect rounded-xl overflow-hidden card-animated">
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead className="bg-gradient-to-r from-yellow-600/20 to-orange-600/20">
              <tr>
                <th className="px-6 py-4 text-left text-sm font-bold text-white uppercase tracking-wider">
                  🏅 Rank
                </th>
                <th className="px-6 py-4 text-left text-sm font-bold text-white uppercase tracking-wider">
                  🤖 Model
                </th>
                <th className="px-6 py-4 text-center text-sm font-bold text-white uppercase tracking-wider">
                  📈 Pass Rate
                </th>
                <th className="px-6 py-4 text-center text-sm font-bold text-white uppercase tracking-wider">
                  ✅ Passed
                </th>
                <th className="px-6 py-4 text-center text-sm font-bold text-white uppercase tracking-wider">
                  ❌ Failed
                </th>
                <th className="px-6 py-4 text-center text-sm font-bold text-white uppercase tracking-wider">
                  ⏳ Pending
                </th>
                <th className="px-6 py-4 text-center text-sm font-bold text-white uppercase tracking-wider">
                  📊 Total
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-white/10">
              {scores.map((score, index) => {
                const rank = index + 1;
                return (
                  <tr key={score.modelId} className="hover:bg-white/5 transition-all duration-300">
                    <td className="px-6 py-6 whitespace-nowrap">
                      <div className={`inline-flex items-center px-4 py-2 rounded-full text-lg font-bold transition-all duration-300 ${getRankColor(rank)} ${rank <= 3 ? 'pulse-animation' : ''}`}>
                        {getRankIcon(rank)}
                      </div>
                    </td>
                    <td className="px-6 py-6 whitespace-nowrap">
                      <div className="card-animated flex items-center gap-3" style={{ animationDelay: `${index * 0.1}s` }}>
                        <div className="flex-shrink-0">
                          {score.imageUrl ? (
                            <img
                              src={score.imageUrl}
                              alt={score.modelName}
                              className="w-10 h-10 rounded-lg object-cover"
                              onError={(e) => {
                                // Fallback to icon or default if image fails to load
                                e.currentTarget.style.display = 'none';
                                const nextElement = e.currentTarget.nextElementSibling as HTMLElement;
                                if (nextElement) nextElement.style.display = 'block';
                              }}
                            />
                          ) : null}
                          <span className="text-2xl" style={{ display: score.imageUrl ? 'none' : 'block' }}>
                            {score.icon || '🤖'}
                          </span>
                        </div>
                        <div>
                          <div className="text-lg font-bold text-white">
                            {score.modelName}
                          </div>
                          <div className="text-white/70">
                            {score.provider}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-6 whitespace-nowrap text-center">
                      <div className={`inline-flex items-center px-4 py-2 rounded-xl text-xl font-bold transition-all duration-300 ${getPassRateColor(score.passRate)} ${rank === 1 ? 'pulse-animation' : ''}`}>
                        {score.passRate.toFixed(1)}%
                      </div>
                    </td>
                    <td className="px-6 py-6 whitespace-nowrap text-center">
                      <span className="inline-flex items-center px-3 py-2 rounded-lg text-sm font-bold bg-gradient-to-r from-green-500 to-emerald-500 text-white">
                        {score.passedQuestions}
                      </span>
                    </td>
                    <td className="px-6 py-6 whitespace-nowrap text-center">
                      <span className="inline-flex items-center px-3 py-2 rounded-lg text-sm font-bold bg-gradient-to-r from-red-500 to-pink-500 text-white">
                        {score.failedQuestions}
                      </span>
                    </td>
                    <td className="px-6 py-6 whitespace-nowrap text-center">
                      <span className="inline-flex items-center px-3 py-2 rounded-lg text-sm font-bold bg-gradient-to-r from-gray-500 to-gray-600 text-white">
                        {score.pendingQuestions}
                      </span>
                    </td>
                    <td className="px-6 py-6 whitespace-nowrap text-center text-lg font-bold text-white">
                      {score.totalQuestions}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      <div className="glass-effect rounded-xl p-6 card-animated">
        <div className="flex items-start gap-4">
          <div className="text-3xl">💡</div>
          <div>
            <h3 className="text-lg font-bold text-white mb-2">
              How the leaderboard works
            </h3>
            <p className="text-white/80 leading-relaxed mb-3">
              Models are ranked by their pass rate (percentage of questions passed).
              The pass rate is calculated as: <span className="font-bold text-white">(Passed Questions ÷ Total Questions) × 100%</span>
            </p>
            {(selectedProviders.length > 0 || selectedCategories.length > 0) && (
              <div className="mt-4 p-3 bg-white/10 rounded-lg">
                <p className="text-white/90 text-sm">
                  <span className="font-semibold">📊 Current view:</span>
                  {selectedProviders.length > 0 && selectedCategories.length > 0 && (
                    <> Showing {selectedProviders.join(', ')} models on {selectedCategories.join(', ')} questions only</>
                  )}
                  {selectedProviders.length > 0 && selectedCategories.length === 0 && (
                    <> Showing {selectedProviders.join(', ')} models only</>
                  )}
                  {selectedProviders.length === 0 && selectedCategories.length > 0 && (
                    <> Showing {selectedCategories.join(', ')} questions only</>
                  )}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Leaderboard;
