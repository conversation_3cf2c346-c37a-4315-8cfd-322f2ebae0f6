"""
Tower of Hanoi Solver
Solves the Tower of Hanoi puzzle for 1-10 disks on 3 pegs.
Outputs the solution moves to a text file.

Usage: Run the script and enter the number of disks (1-10) when prompted.
The solution will be saved to 'hanoi_solution.txt'
"""

def solve_hanoi(n, from_peg, to_peg, aux_peg, moves_list):
    """
    Recursive function to solve Tower of Hanoi and collect moves.

    Args:
        n: Number of disks to move
        from_peg: Source peg (0, 1, or 2)
        to_peg: Destination peg (0, 1, or 2)
        aux_peg: Auxiliary peg (0, 1, or 2)
        moves_list: List to collect all moves
    """
    if n == 0:
        return

    # Move n-1 disks from source to auxiliary peg
    solve_hanoi(n-1, from_peg, aux_peg, to_peg, moves_list)

    # Move the largest disk from source to destination
    moves_list.append([n, from_peg, to_peg])

    # Move n-1 disks from auxiliary to destination peg
    solve_hanoi(n-1, aux_peg, to_peg, from_peg, moves_list)

def get_initial_state(n):
    """
    Generate the initial state with all disks on peg 0.

    Args:
        n: Number of disks

    Returns:
        List representing initial state [peg0, peg1, peg2]
    """
    # All disks start on peg 0, largest (n) at bottom, smallest (1) at top
    peg0 = list(range(n, 0, -1))  # [n, n-1, ..., 2, 1]
    return [peg0, [], []]

def simulate_moves(initial_state, moves):
    """
    Simulate the moves to get the final state.

    Args:
        initial_state: Starting configuration
        moves: List of moves to execute

    Returns:
        Final state after all moves
    """
    # Create a copy to avoid modifying the original
    state = [peg[:] for peg in initial_state]

    for move in moves:
        disk, from_peg, to_peg = move
        # Remove disk from source peg and add to destination peg
        if state[from_peg] and state[from_peg][-1] == disk:
            state[from_peg].pop()
            state[to_peg].append(disk)

    return state

def write_solution_to_file(n, initial_state, moves, final_state, filename="hanoi_solution.txt"):
    """
    Write the complete solution to a text file.

    Args:
        n: Number of disks
        initial_state: Starting configuration
        moves: List of all moves
        final_state: Ending configuration
        filename: Output file name
    """
    with open(filename, 'w') as f:
        f.write(f"Tower of Hanoi Solution for {n} disks\n")
        f.write("=" * 40 + "\n\n")

        f.write(f"Initial state: {initial_state}\n")
        f.write(f"Target: Move all disks from peg 0 to peg 2\n\n")

        f.write("Moves (format: [disk_number, from_peg, to_peg]):\n")
        f.write(f"moves = {moves}\n\n")

        f.write("Move-by-move explanation:\n")
        for i, move in enumerate(moves, 1):
            disk, from_peg, to_peg = move
            f.write(f"{i:2d}. Move disk {disk} from peg {from_peg} to peg {to_peg}\n")

        f.write(f"\nFinal state: {final_state}\n")
        f.write(f"Total moves: {len(moves)}\n")
        f.write(f"Minimum moves required: {2**n - 1}\n")

def get_user_input():
    """
    Get and validate user input for number of disks.

    Returns:
        Number of disks (1-10)
    """
    while True:
        try:
            print("\n" + "="*50)
            print("TOWER OF HANOI SOLVER")
            print("="*50)
            print("This program solves the Tower of Hanoi puzzle.")
            print("- Works with 1-10 disks on 3 pegs (numbered 0, 1, 2)")
            print("- All disks start on peg 0")
            print("- Goal: Move all disks to peg 2")
            print("- Larger disks cannot be placed on smaller disks")
            print("-"*50)

            n = int(input("Enter the number of disks (1-10): "))

            if 1 <= n <= 10:
                return n
            else:
                print("❌ Error: Please enter a number between 1 and 10.")

        except ValueError:
            print("❌ Error: Please enter a valid integer.")

def main():
    """Main function to run the Tower of Hanoi solver."""
    # Get number of disks from user
    n = get_user_input()

    print(f"\n🔄 Solving Tower of Hanoi with {n} disks...")

    # Initialize
    moves = []
    initial_state = get_initial_state(n)

    # Solve the puzzle
    solve_hanoi(n, 0, 2, 1, moves)  # Move from peg 0 to peg 2 using peg 1 as auxiliary

    # Get final state
    final_state = simulate_moves(initial_state, moves)

    # Write solution to file
    filename = "hanoi_solution.txt"
    write_solution_to_file(n, initial_state, moves, final_state, filename)

    # Display results
    print(f"✅ Solution found!")
    print(f"📁 Results saved to: {filename}")
    print(f"📊 Total moves: {len(moves)}")
    print(f"📊 Minimum possible moves: {2**n - 1}")
    print(f"🎯 Initial state: {initial_state}")
    print(f"🎯 Final state: {final_state}")

    # Show first few moves as preview
    print(f"\n📋 First few moves:")
    for i, move in enumerate(moves[:min(5, len(moves))], 1):
        disk, from_peg, to_peg = move
        print(f"  {i}. Move disk {disk} from peg {from_peg} to peg {to_peg}")

    if len(moves) > 5:
        print(f"  ... and {len(moves) - 5} more moves (see {filename} for complete list)")

if __name__ == "__main__":
    main()